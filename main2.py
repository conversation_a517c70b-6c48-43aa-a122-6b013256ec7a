import colour
import numpy as np
from pathlib import Path
import csv
import traceback
import sys

def read_data_between_markers(file_path):
    data = []
    reading = False
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()
            if line == 'BEGIN_DATA':
                reading = True
                continue
            if line == 'END_DATA':
                reading = False
                break
            if reading:
                # 解析数据行
                values = line.split()
                data.append([float(val) if '.' in val else int(val) for val in values])
    return np.array(data)

def lab_to_hsv(lab_data):
    hsv_data = []
    for row in lab_data:
        # 提取 Lab 值（第 2、3、4 列）
        L, a, b = row[0], row[1], row[2]
        lab = [L, a, b]
        # 将 Lab 转换为 XYZ
        xyz = colour.Lab_to_XYZ(lab)
        # 将 XYZ 转换为 RGB
        rgb = colour.XYZ_to_sRGB(xyz)
        # 将 RGB 转换为 HSV
        hsv = colour.RGB_to_HSV(rgb)
        hsv_data.append(hsv)
    return hsv_data

def resource_path(relative_path):
    """获取打包后资源的绝对路径"""
    if hasattr(sys, '_MEIPASS'):  # 打包后会有这个属性
        base_path = Path(sys._MEIPASS)
    else:  # 开发环境
        base_path = Path(__file__).parent
    return base_path / relative_path

def save_to_csv(data, filename, headers):
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            writer.writerows(data)
    except Exception as e:
        print(f"保存 CSV 失败: {e}")
        # 打印日志文件路径进行调试
        log_path = resource_path('error.log')
        print(f"尝试写入日志文件: {log_path}")
        try:
            with open(log_path, 'w') as f:
                f.write(f"错误: {str(e)}\n")
                f.write(f"追踪信息: {traceback.format_exc()}")
        except Exception as log_error:
            print(f"写入日志文件失败: {log_error}")

if __name__ == "__main__":
    try:
        # 使用 resource_path 函数获取文件路径
        file_path = resource_path('透射红绿蓝测试.txt')
        result = read_data_between_markers(file_path)
        hsv = lab_to_hsv(result[:, 1:])

        # 创建输出目录（如果不存在）
        output_dir = Path('')
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存合并数据到 CSV
        combined_csv_path = output_dir / 'lab_hsv_combined.csv'
        headers = ['ID', 'L*', 'a*', 'b*', 'H', 'S', 'B']

        # 合并数据（假设result和hsv的ID列相同）
        combined_data = np.hstack((result[:, :4], hsv))

        # 写入 CSV（使用 np.savetxt）
        np.savetxt(
            combined_csv_path,
            combined_data,
            delimiter=',',
            header=','.join(headers),
            comments='',
            fmt='%.6f'  # 调整浮点数精度
        )
    except Exception as e:
        print(f"程序运行出错: {e}")
        # 打印日志文件路径进行调试
        log_path = resource_path('error.log')
        print(f"尝试写入日志文件: {log_path}")
        try:
            with open(log_path, 'w') as f:
                f.write(f"错误: {str(e)}\n")
                f.write(f"追踪信息: {traceback.format_exc()}")
        except Exception as log_error:
            print(f"写入日志文件失败: {log_error}")
    input()