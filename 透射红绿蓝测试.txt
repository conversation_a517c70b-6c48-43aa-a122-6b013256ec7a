ORIGINATOR	"BabelColor PatchTool, version 4.5.0 b332"
LGOROWLENGTH	40
CREATED	"2025-07-29"  # Time: 14:46:29
INSTRUMENTATION	"i1Pro / i1Pro 2 (XRGA) (Correction Matrix - none)"
INSTRUMENT_SN	"1018196"
MEASUREMENT_SOURCE	"Illumination=Emission"
ILLUMINATION_NAME	"Emission"
OBSERVER_ANGLE	"2"
WEIGHTING_FUNCTION	"ILLUMINANT, Emission"
WEIGHTING_FUNCTION	"OBSERVER, 2 degree"
KEYWORD	"DEVCALSTD"
DEVCALSTD	"XRGA"
#
# PatchTool DISPLAY-READER TOOL - RAW MEASUREMENTS
#
# WARNING: The measured color data is emission data which cannot be processed like reflectance data.
#       As a consequence, when this file is opened by a program which expects reflectance data, the processed
#       color values will be wrong. This file is primarily intended for those who want to process the data on their own.
#
# Note: L*a*b* data is relative to XYZ of the measured White Point.
#
KEYWORD	"SAMPLE_ID"
NUMBER_OF_FIELDS	4
BEGIN_DATA_FORMAT
SAMPLE_ID	LAB_L	LAB_A	LAB_B
END_DATA_FORMAT
NUMBER_OF_SETS	3
BEGIN_DATA
1	55.835	4.737	-52.188
2	77.574	-33.992	39.004
3	61.274	64.548	8.422
END_DATA

# Measured White Point:
#       Luminance: 3044.177 cd/m2
#       CCT: 5095 K
#       XYZ: (98.376 / 100 / 88.945)
BEGIN_DATA_WHITEPOINT
KEYWORD	"SAMPLE_ID"
NUMBER_OF_FIELDS	4
BEGIN_DATA_FORMAT
SAMPLE_ID	LAB_L	LAB_A	LAB_B
END_DATA_FORMAT
NUMBER_OF_SETS	1
BEGIN_DATA
0	100.000	0.000	0.000
END_DATA
END_DATA_WHITEPOINT
