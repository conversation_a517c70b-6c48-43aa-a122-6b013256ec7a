('E:\\7-31\\build\\ColorConverter\\PYZ-00.pyz',
 [('PIL',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\calendar.py', 'PYMODULE'),
  ('cgi', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\cgi.py', 'PYMODULE'),
  ('cmd', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\colorsys.py', 'PYMODULE'),
  ('colour',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\__init__.py',
   'PYMODULE'),
  ('colour.adaptation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\__init__.py',
   'PYMODULE'),
  ('colour.adaptation.cie1994',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\cie1994.py',
   'PYMODULE'),
  ('colour.adaptation.cmccat2000',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\cmccat2000.py',
   'PYMODULE'),
  ('colour.adaptation.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.adaptation.datasets.cat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\datasets\\cat.py',
   'PYMODULE'),
  ('colour.adaptation.fairchild1990',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\fairchild1990.py',
   'PYMODULE'),
  ('colour.adaptation.vonkries',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\vonkries.py',
   'PYMODULE'),
  ('colour.adaptation.zhai2018',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\adaptation\\zhai2018.py',
   'PYMODULE'),
  ('colour.algebra',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\__init__.py',
   'PYMODULE'),
  ('colour.algebra.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\common.py',
   'PYMODULE'),
  ('colour.algebra.coordinates',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\coordinates\\__init__.py',
   'PYMODULE'),
  ('colour.algebra.coordinates.transformations',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\coordinates\\transformations.py',
   'PYMODULE'),
  ('colour.algebra.extrapolation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\extrapolation.py',
   'PYMODULE'),
  ('colour.algebra.interpolation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\interpolation.py',
   'PYMODULE'),
  ('colour.algebra.prng',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\prng.py',
   'PYMODULE'),
  ('colour.algebra.regression',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\algebra\\regression.py',
   'PYMODULE'),
  ('colour.appearance',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\__init__.py',
   'PYMODULE'),
  ('colour.appearance.atd95',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\atd95.py',
   'PYMODULE'),
  ('colour.appearance.cam16',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\cam16.py',
   'PYMODULE'),
  ('colour.appearance.ciecam02',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\ciecam02.py',
   'PYMODULE'),
  ('colour.appearance.ciecam16',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\ciecam16.py',
   'PYMODULE'),
  ('colour.appearance.hellwig2022',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\hellwig2022.py',
   'PYMODULE'),
  ('colour.appearance.hke',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\hke.py',
   'PYMODULE'),
  ('colour.appearance.hunt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\hunt.py',
   'PYMODULE'),
  ('colour.appearance.kim2009',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\kim2009.py',
   'PYMODULE'),
  ('colour.appearance.llab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\llab.py',
   'PYMODULE'),
  ('colour.appearance.nayatani95',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\nayatani95.py',
   'PYMODULE'),
  ('colour.appearance.rlab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\rlab.py',
   'PYMODULE'),
  ('colour.appearance.zcam',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\appearance\\zcam.py',
   'PYMODULE'),
  ('colour.biochemistry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\biochemistry\\__init__.py',
   'PYMODULE'),
  ('colour.biochemistry.michaelis_menten',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\biochemistry\\michaelis_menten.py',
   'PYMODULE'),
  ('colour.blindness',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\blindness\\__init__.py',
   'PYMODULE'),
  ('colour.blindness.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\blindness\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.blindness.datasets.machado2010',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\blindness\\datasets\\machado2010.py',
   'PYMODULE'),
  ('colour.blindness.machado2009',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\blindness\\machado2009.py',
   'PYMODULE'),
  ('colour.characterisation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.aces_it',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\aces_it.py',
   'PYMODULE'),
  ('colour.characterisation.cameras',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\cameras.py',
   'PYMODULE'),
  ('colour.characterisation.correction',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\correction.py',
   'PYMODULE'),
  ('colour.characterisation.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.aces_it',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\aces_it.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.cameras',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\cameras\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.cameras.dslr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\cameras\\dslr\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.cameras.dslr.sensitivities',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\cameras\\dslr\\sensitivities.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.colour_checkers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\colour_checkers\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.colour_checkers.chromaticity_coordinates',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\colour_checkers\\chromaticity_coordinates.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.colour_checkers.sds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\colour_checkers\\sds.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.displays',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\displays\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.displays.crt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\displays\\crt\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.displays.crt.primaries',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\displays\\crt\\primaries.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.displays.lcd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\displays\\lcd\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.displays.lcd.primaries',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\displays\\lcd\\primaries.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.filters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\filters\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.filters.sds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\filters\\sds.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.lenses',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\lenses\\__init__.py',
   'PYMODULE'),
  ('colour.characterisation.datasets.lenses.sds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\datasets\\lenses\\sds.py',
   'PYMODULE'),
  ('colour.characterisation.displays',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\characterisation\\displays.py',
   'PYMODULE'),
  ('colour.colorimetry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\__init__.py',
   'PYMODULE'),
  ('colour.colorimetry.blackbody',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\blackbody.py',
   'PYMODULE'),
  ('colour.colorimetry.cmfs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\cmfs.py',
   'PYMODULE'),
  ('colour.colorimetry.correction',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\correction.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.cmfs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\cmfs.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.illuminants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\illuminants\\__init__.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.illuminants.chromaticity_coordinates',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\illuminants\\chromaticity_coordinates.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.illuminants.hunterlab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\illuminants\\hunterlab.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.illuminants.sds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\illuminants\\sds.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.illuminants.sds_d_illuminant_series',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\illuminants\\sds_d_illuminant_series.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.illuminants.tristimulus_values',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\illuminants\\tristimulus_values.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.lefs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\lefs.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.light_sources',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\light_sources\\__init__.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.light_sources.chromaticity_coordinates',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\light_sources\\chromaticity_coordinates.py',
   'PYMODULE'),
  ('colour.colorimetry.datasets.light_sources.sds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\datasets\\light_sources\\sds.py',
   'PYMODULE'),
  ('colour.colorimetry.dominant',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\dominant.py',
   'PYMODULE'),
  ('colour.colorimetry.generation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\generation.py',
   'PYMODULE'),
  ('colour.colorimetry.illuminants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\illuminants.py',
   'PYMODULE'),
  ('colour.colorimetry.lefs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\lefs.py',
   'PYMODULE'),
  ('colour.colorimetry.lightness',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\lightness.py',
   'PYMODULE'),
  ('colour.colorimetry.luminance',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\luminance.py',
   'PYMODULE'),
  ('colour.colorimetry.photometry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\photometry.py',
   'PYMODULE'),
  ('colour.colorimetry.spectrum',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\spectrum.py',
   'PYMODULE'),
  ('colour.colorimetry.transformations',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\transformations.py',
   'PYMODULE'),
  ('colour.colorimetry.tristimulus_values',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\tristimulus_values.py',
   'PYMODULE'),
  ('colour.colorimetry.uniformity',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\uniformity.py',
   'PYMODULE'),
  ('colour.colorimetry.whiteness',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\whiteness.py',
   'PYMODULE'),
  ('colour.colorimetry.yellowness',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\colorimetry\\yellowness.py',
   'PYMODULE'),
  ('colour.constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\constants\\__init__.py',
   'PYMODULE'),
  ('colour.constants.cie',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\constants\\cie.py',
   'PYMODULE'),
  ('colour.constants.codata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\constants\\codata.py',
   'PYMODULE'),
  ('colour.constants.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\constants\\common.py',
   'PYMODULE'),
  ('colour.continuous',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\continuous\\__init__.py',
   'PYMODULE'),
  ('colour.continuous.abstract',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\continuous\\abstract.py',
   'PYMODULE'),
  ('colour.continuous.multi_signals',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\continuous\\multi_signals.py',
   'PYMODULE'),
  ('colour.continuous.signal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\continuous\\signal.py',
   'PYMODULE'),
  ('colour.contrast',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\contrast\\__init__.py',
   'PYMODULE'),
  ('colour.contrast.barten1999',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\contrast\\barten1999.py',
   'PYMODULE'),
  ('colour.corresponding',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\corresponding\\__init__.py',
   'PYMODULE'),
  ('colour.corresponding.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\corresponding\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.corresponding.datasets.breneman1987',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\corresponding\\datasets\\breneman1987.py',
   'PYMODULE'),
  ('colour.corresponding.prediction',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\corresponding\\prediction.py',
   'PYMODULE'),
  ('colour.difference',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\__init__.py',
   'PYMODULE'),
  ('colour.difference.cam02_ucs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\cam02_ucs.py',
   'PYMODULE'),
  ('colour.difference.cam16_ucs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\cam16_ucs.py',
   'PYMODULE'),
  ('colour.difference.delta_e',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\delta_e.py',
   'PYMODULE'),
  ('colour.difference.din99',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\din99.py',
   'PYMODULE'),
  ('colour.difference.huang2015',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\huang2015.py',
   'PYMODULE'),
  ('colour.difference.stress',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\difference\\stress.py',
   'PYMODULE'),
  ('colour.geometry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\geometry\\__init__.py',
   'PYMODULE'),
  ('colour.geometry.ellipse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\geometry\\ellipse.py',
   'PYMODULE'),
  ('colour.geometry.intersection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\geometry\\intersection.py',
   'PYMODULE'),
  ('colour.geometry.primitives',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\geometry\\primitives.py',
   'PYMODULE'),
  ('colour.geometry.section',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\geometry\\section.py',
   'PYMODULE'),
  ('colour.geometry.vertices',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\geometry\\vertices.py',
   'PYMODULE'),
  ('colour.graph',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\graph\\__init__.py',
   'PYMODULE'),
  ('colour.graph.conversion',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\graph\\conversion.py',
   'PYMODULE'),
  ('colour.hints',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\hints\\__init__.py',
   'PYMODULE'),
  ('colour.io',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\__init__.py',
   'PYMODULE'),
  ('colour.io.ctl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\ctl.py',
   'PYMODULE'),
  ('colour.io.image',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\image.py',
   'PYMODULE'),
  ('colour.io.luts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\__init__.py',
   'PYMODULE'),
  ('colour.io.luts.cinespace_csp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\cinespace_csp.py',
   'PYMODULE'),
  ('colour.io.luts.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\common.py',
   'PYMODULE'),
  ('colour.io.luts.iridas_cube',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\iridas_cube.py',
   'PYMODULE'),
  ('colour.io.luts.lut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\lut.py',
   'PYMODULE'),
  ('colour.io.luts.operator',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\operator.py',
   'PYMODULE'),
  ('colour.io.luts.resolve_cube',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\resolve_cube.py',
   'PYMODULE'),
  ('colour.io.luts.sequence',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\sequence.py',
   'PYMODULE'),
  ('colour.io.luts.sony_spi1d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\sony_spi1d.py',
   'PYMODULE'),
  ('colour.io.luts.sony_spi3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\sony_spi3d.py',
   'PYMODULE'),
  ('colour.io.luts.sony_spimtx',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\luts\\sony_spimtx.py',
   'PYMODULE'),
  ('colour.io.ocio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\ocio.py',
   'PYMODULE'),
  ('colour.io.tabular',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\tabular.py',
   'PYMODULE'),
  ('colour.io.tm2714',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\tm2714.py',
   'PYMODULE'),
  ('colour.io.uprtek_sekonic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\uprtek_sekonic.py',
   'PYMODULE'),
  ('colour.io.xrite',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\io\\xrite.py',
   'PYMODULE'),
  ('colour.models',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\__init__.py',
   'PYMODULE'),
  ('colour.models.cam02_ucs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cam02_ucs.py',
   'PYMODULE'),
  ('colour.models.cam16_ucs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cam16_ucs.py',
   'PYMODULE'),
  ('colour.models.cie_lab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cie_lab.py',
   'PYMODULE'),
  ('colour.models.cie_luv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cie_luv.py',
   'PYMODULE'),
  ('colour.models.cie_ucs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cie_ucs.py',
   'PYMODULE'),
  ('colour.models.cie_uvw',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cie_uvw.py',
   'PYMODULE'),
  ('colour.models.cie_xyy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\cie_xyy.py',
   'PYMODULE'),
  ('colour.models.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\common.py',
   'PYMODULE'),
  ('colour.models.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.models.datasets.macadam_ellipses',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\datasets\\macadam_ellipses.py',
   'PYMODULE'),
  ('colour.models.datasets.pointer_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\datasets\\pointer_gamut.py',
   'PYMODULE'),
  ('colour.models.din99',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\din99.py',
   'PYMODULE'),
  ('colour.models.hdr_cie_lab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\hdr_cie_lab.py',
   'PYMODULE'),
  ('colour.models.hdr_ipt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\hdr_ipt.py',
   'PYMODULE'),
  ('colour.models.hunter_lab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\hunter_lab.py',
   'PYMODULE'),
  ('colour.models.hunter_rdab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\hunter_rdab.py',
   'PYMODULE'),
  ('colour.models.icacb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\icacb.py',
   'PYMODULE'),
  ('colour.models.igpgtg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\igpgtg.py',
   'PYMODULE'),
  ('colour.models.ipt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\ipt.py',
   'PYMODULE'),
  ('colour.models.jzazbz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\jzazbz.py',
   'PYMODULE'),
  ('colour.models.oklab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\oklab.py',
   'PYMODULE'),
  ('colour.models.osa_ucs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\osa_ucs.py',
   'PYMODULE'),
  ('colour.models.prolab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\prolab.py',
   'PYMODULE'),
  ('colour.models.ragoo2021',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\ragoo2021.py',
   'PYMODULE'),
  ('colour.models.rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\__init__.py',
   'PYMODULE'),
  ('colour.models.rgb.cmyk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\cmyk.py',
   'PYMODULE'),
  ('colour.models.rgb.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\common.py',
   'PYMODULE'),
  ('colour.models.rgb.cylindrical',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\cylindrical.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.aces',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\aces.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.adobe_rgb_1998',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\adobe_rgb_1998.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.adobe_wide_gamut_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\adobe_wide_gamut_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.apple_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\apple_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.arri',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\arri.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.best_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\best_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.beta_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\beta_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.blackmagic_design',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\blackmagic_design.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.canon_cinema_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\canon_cinema_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.cie_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\cie_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.color_match_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\color_match_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.davinci_wide_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\davinci_wide_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.dcdm_xyz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\dcdm_xyz.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.dci_p3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\dci_p3.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.display_p3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\display_p3.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.dji_d_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\dji_d_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.don_rgb_4',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\don_rgb_4.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.ebu_3213_e',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\ebu_3213_e.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.eci_rgb_v2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\eci_rgb_v2.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.ekta_space_ps5',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\ekta_space_ps5.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.filmlight_e_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\filmlight_e_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.fujifilm_f_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\fujifilm_f_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.gopro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\gopro.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.itur_bt_2020',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\itur_bt_2020.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.itur_bt_470',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\itur_bt_470.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.itur_bt_709',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\itur_bt_709.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.itut_h_273',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\itut_h_273.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.max_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\max_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.nikon_n_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\nikon_n_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.ntsc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\ntsc.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.p3_d65',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\p3_d65.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.pal_secam',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\pal_secam.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.panasonic_v_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\panasonic_v_gamut.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.plasa_ansi_e154',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\plasa_ansi_e154.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.red',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\red.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.rimm_romm_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\rimm_romm_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.russell_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\russell_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.sharp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\sharp.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.smpte_240m',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\smpte_240m.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.smpte_c',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\smpte_c.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.sony',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\sony.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.srgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\srgb.py',
   'PYMODULE'),
  ('colour.models.rgb.datasets.xtreme_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\datasets\\xtreme_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.derivation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\derivation.py',
   'PYMODULE'),
  ('colour.models.rgb.hanbury2003',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\hanbury2003.py',
   'PYMODULE'),
  ('colour.models.rgb.ictcp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\ictcp.py',
   'PYMODULE'),
  ('colour.models.rgb.itut_h_273',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\itut_h_273.py',
   'PYMODULE'),
  ('colour.models.rgb.prismatic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\prismatic.py',
   'PYMODULE'),
  ('colour.models.rgb.rgb_colourspace',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\rgb_colourspace.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\__init__.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.aces',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\aces.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.apple_log_profile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\apple_log_profile.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.arib_std_b67',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\arib_std_b67.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.arri',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\arri.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.blackmagic_design',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\blackmagic_design.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.canon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\canon.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.cineon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\cineon.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\common.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.davinci_intermediate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\davinci_intermediate.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.dcdm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\dcdm.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.dicom_gsdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\dicom_gsdf.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.dji_d_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\dji_d_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.exponent',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\exponent.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.filmic_pro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\filmic_pro.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.filmlight_t_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\filmlight_t_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.fujifilm_f_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\fujifilm_f_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.gamma',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\gamma.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.gopro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\gopro.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itur_bt_1361',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itur_bt_1361.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itur_bt_1886',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itur_bt_1886.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itur_bt_2020',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itur_bt_2020.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itur_bt_2100',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itur_bt_2100.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itur_bt_601',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itur_bt_601.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itur_bt_709',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itur_bt_709.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.itut_h_273',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\itut_h_273.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.leica_l_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\leica_l_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.linear',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\linear.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.nikon_n_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\nikon_n_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.panalog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\panalog.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.panasonic_v_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\panasonic_v_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.pivoted_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\pivoted_log.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.red',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\red.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.rimm_romm_rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\rimm_romm_rgb.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.smpte_240m',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\smpte_240m.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.sony',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\sony.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.srgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\srgb.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.st_2084',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\st_2084.py',
   'PYMODULE'),
  ('colour.models.rgb.transfer_functions.viper_log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\transfer_functions\\viper_log.py',
   'PYMODULE'),
  ('colour.models.rgb.ycbcr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\ycbcr.py',
   'PYMODULE'),
  ('colour.models.rgb.ycocg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\rgb\\ycocg.py',
   'PYMODULE'),
  ('colour.models.yrg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\models\\yrg.py',
   'PYMODULE'),
  ('colour.notation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\__init__.py',
   'PYMODULE'),
  ('colour.notation.css_color_3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\css_color_3.py',
   'PYMODULE'),
  ('colour.notation.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.notation.datasets.css_color_3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\datasets\\css_color_3.py',
   'PYMODULE'),
  ('colour.notation.datasets.munsell',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\datasets\\munsell\\__init__.py',
   'PYMODULE'),
  ('colour.notation.datasets.munsell.all',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\datasets\\munsell\\all.py',
   'PYMODULE'),
  ('colour.notation.datasets.munsell.experimental',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\datasets\\munsell\\experimental.py',
   'PYMODULE'),
  ('colour.notation.datasets.munsell.real',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\datasets\\munsell\\real.py',
   'PYMODULE'),
  ('colour.notation.hexadecimal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\hexadecimal.py',
   'PYMODULE'),
  ('colour.notation.munsell',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\notation\\munsell.py',
   'PYMODULE'),
  ('colour.phenomena',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\phenomena\\__init__.py',
   'PYMODULE'),
  ('colour.phenomena.rayleigh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\phenomena\\rayleigh.py',
   'PYMODULE'),
  ('colour.plotting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\__init__.py',
   'PYMODULE'),
  ('colour.plotting.blindness',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\blindness.py',
   'PYMODULE'),
  ('colour.plotting.characterisation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\characterisation.py',
   'PYMODULE'),
  ('colour.plotting.colorimetry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\colorimetry.py',
   'PYMODULE'),
  ('colour.plotting.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\common.py',
   'PYMODULE'),
  ('colour.plotting.corresponding',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\corresponding.py',
   'PYMODULE'),
  ('colour.plotting.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.plotting.datasets.astm_g_173',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\datasets\\astm_g_173.py',
   'PYMODULE'),
  ('colour.plotting.diagrams',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\diagrams.py',
   'PYMODULE'),
  ('colour.plotting.graph',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\graph.py',
   'PYMODULE'),
  ('colour.plotting.models',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\models.py',
   'PYMODULE'),
  ('colour.plotting.notation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\notation.py',
   'PYMODULE'),
  ('colour.plotting.phenomena',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\phenomena.py',
   'PYMODULE'),
  ('colour.plotting.quality',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\quality.py',
   'PYMODULE'),
  ('colour.plotting.section',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\section.py',
   'PYMODULE'),
  ('colour.plotting.temperature',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\temperature.py',
   'PYMODULE'),
  ('colour.plotting.tm3018',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\tm3018\\__init__.py',
   'PYMODULE'),
  ('colour.plotting.tm3018.components',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\tm3018\\components.py',
   'PYMODULE'),
  ('colour.plotting.tm3018.report',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\tm3018\\report.py',
   'PYMODULE'),
  ('colour.plotting.volume',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\plotting\\volume.py',
   'PYMODULE'),
  ('colour.quality',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\__init__.py',
   'PYMODULE'),
  ('colour.quality.cfi2017',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\cfi2017.py',
   'PYMODULE'),
  ('colour.quality.cqs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\cqs.py',
   'PYMODULE'),
  ('colour.quality.cri',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\cri.py',
   'PYMODULE'),
  ('colour.quality.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.quality.datasets.tcs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\datasets\\tcs.py',
   'PYMODULE'),
  ('colour.quality.datasets.vs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\datasets\\vs.py',
   'PYMODULE'),
  ('colour.quality.ssi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\ssi.py',
   'PYMODULE'),
  ('colour.quality.tm3018',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\quality\\tm3018.py',
   'PYMODULE'),
  ('colour.recovery',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\__init__.py',
   'PYMODULE'),
  ('colour.recovery.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.recovery.datasets.dyer2017',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\datasets\\dyer2017.py',
   'PYMODULE'),
  ('colour.recovery.datasets.mallett2019',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\datasets\\mallett2019.py',
   'PYMODULE'),
  ('colour.recovery.datasets.otsu2018',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\datasets\\otsu2018.py',
   'PYMODULE'),
  ('colour.recovery.datasets.smits1999',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\datasets\\smits1999.py',
   'PYMODULE'),
  ('colour.recovery.jakob2019',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\jakob2019.py',
   'PYMODULE'),
  ('colour.recovery.jiang2013',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\jiang2013.py',
   'PYMODULE'),
  ('colour.recovery.mallett2019',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\mallett2019.py',
   'PYMODULE'),
  ('colour.recovery.meng2015',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\meng2015.py',
   'PYMODULE'),
  ('colour.recovery.otsu2018',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\otsu2018.py',
   'PYMODULE'),
  ('colour.recovery.smits1999',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\recovery\\smits1999.py',
   'PYMODULE'),
  ('colour.temperature',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\__init__.py',
   'PYMODULE'),
  ('colour.temperature.cie_d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\cie_d.py',
   'PYMODULE'),
  ('colour.temperature.hernandez1999',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\hernandez1999.py',
   'PYMODULE'),
  ('colour.temperature.kang2002',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\kang2002.py',
   'PYMODULE'),
  ('colour.temperature.krystek1985',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\krystek1985.py',
   'PYMODULE'),
  ('colour.temperature.mccamy1992',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\mccamy1992.py',
   'PYMODULE'),
  ('colour.temperature.ohno2013',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\ohno2013.py',
   'PYMODULE'),
  ('colour.temperature.planck1900',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\planck1900.py',
   'PYMODULE'),
  ('colour.temperature.robertson1968',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\temperature\\robertson1968.py',
   'PYMODULE'),
  ('colour.utilities',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\__init__.py',
   'PYMODULE'),
  ('colour.utilities.array',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\array.py',
   'PYMODULE'),
  ('colour.utilities.callback',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\callback.py',
   'PYMODULE'),
  ('colour.utilities.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\common.py',
   'PYMODULE'),
  ('colour.utilities.data_structures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\data_structures.py',
   'PYMODULE'),
  ('colour.utilities.deprecation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\deprecation.py',
   'PYMODULE'),
  ('colour.utilities.documentation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\documentation.py',
   'PYMODULE'),
  ('colour.utilities.metrics',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\metrics.py',
   'PYMODULE'),
  ('colour.utilities.verbose',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\utilities\\verbose.py',
   'PYMODULE'),
  ('colour.volume',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\__init__.py',
   'PYMODULE'),
  ('colour.volume.datasets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\datasets\\__init__.py',
   'PYMODULE'),
  ('colour.volume.datasets.optimal_colour_stimuli',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\datasets\\optimal_colour_stimuli.py',
   'PYMODULE'),
  ('colour.volume.macadam_limits',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\macadam_limits.py',
   'PYMODULE'),
  ('colour.volume.mesh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\mesh.py',
   'PYMODULE'),
  ('colour.volume.pointer_gamut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\pointer_gamut.py',
   'PYMODULE'),
  ('colour.volume.rgb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\rgb.py',
   'PYMODULE'),
  ('colour.volume.spectrum',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\colour\\volume\\spectrum.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('cycler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\doctest.py', 'PYMODULE'),
  ('email',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\http\\server.py',
   'PYMODULE'),
  ('imageio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\__init__.py',
   'PYMODULE'),
  ('imageio.config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\config\\__init__.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\config\\extensions.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\config\\plugins.py',
   'PYMODULE'),
  ('imageio.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\__init__.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\findlib.py',
   'PYMODULE'),
  ('imageio.core.format',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\format.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\imopen.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.core.request',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\request.py',
   'PYMODULE'),
  ('imageio.core.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\util.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\core\\v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.plugins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\_bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._swf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\_swf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\_tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\bsdf.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\dicom.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\example.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\feisem.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\fits.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\gdal.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\grab.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\lytro.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\npz.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\opencv.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\pillow.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\pillowmulti.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\pyav.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\spe.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\swf.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\plugins\\tifffile_v3.py',
   'PYMODULE'),
  ('imageio.typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\typing.py',
   'PYMODULE'),
  ('imageio.v2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\v2.py',
   'PYMODULE'),
  ('imageio.v3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\imageio\\v3.py',
   'PYMODULE'),
  ('imp', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\lzma.py', 'PYMODULE'),
  ('matplotlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\runpy.py', 'PYMODULE'),
  ('scipy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy.__config__',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy._lib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('scipy._lib._array_api_compat_vendor',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_array_api_compat_vendor.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib._sparse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_sparse.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._fft',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.fft',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._delegation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_delegation.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._at',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_at.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._funcs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._lazy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_lazy.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_compat.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._helpers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._typing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra.testing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\testing.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.pyprima',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.cobyla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\cobyla.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.cobylb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\cobylb.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.geometry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.initialize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\initialize.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.trustregion',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\trustregion.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.update',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\update.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._bounds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_bounds.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._linear_constraints',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_linear_constraints.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._nonlinear_constraints',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_nonlinear_constraints.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._project',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_project.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.checkbreak',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\checkbreak.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.consts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\consts.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.evaluate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\evaluate.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.history',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\history.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.infos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\infos.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.message',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\message.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.powalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\powalg.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.preproc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\preproc.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.present',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\present.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.ratio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\ratio.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.redrho',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\redrho.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.selectx',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\selectx.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy.constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.fft',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.integrate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.optimize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._elementwise',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_elementwise.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize.elementwise',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\elementwise.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.sparse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.spatial',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.special',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.stats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._finite_differences',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_finite_differences.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats._quantile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_quantile.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('secrets', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('token', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('xlrd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\__init__.py',
   'PYMODULE'),
  ('xlrd.biffh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE'),
  ('xlrd.book',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\book.py',
   'PYMODULE'),
  ('xlrd.compdoc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE'),
  ('xlrd.formatting',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE'),
  ('xlrd.formula',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE'),
  ('xlrd.info',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\info.py',
   'PYMODULE'),
  ('xlrd.sheet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE'),
  ('xlrd.timemachine',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE'),
  ('xlrd.xldate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE'),
  ('xml', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\zipimport.py',
   'PYMODULE')])
