('E:\\7-31\\build\\ColorConverter\\ColorConverter.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'E:\\7-31\\build\\ColorConverter\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'E:\\7-31\\build\\ColorConverter\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\7-31\\build\\ColorConverter\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\7-31\\build\\ColorConverter\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\7-31\\build\\ColorConverter\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\7-31\\build\\ColorConverter\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('main2', 'E:\\7-31\\main2.py', 'PYSOURCE'),
  ('python311.dll', 'D:\\Anaconda3\\envs\\COLORSPACE\\python311.dll', 'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'BINARY'),
  ('_ctypes.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_schur_sqrtm.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_schur_sqrtm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_direct.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_interpnd.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_dierckx.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rigid_transform.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\transform\\_rigid_transform.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_avif.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\_tkagg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\ft2font.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_tri.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_qhull.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\contourpy\\_contourpy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_image.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\kiwisolver\\_cext.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\_path.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_cyutility.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_cyutility.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqplib.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_slsqplib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_dop.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_vode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_gufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_special_ufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_comb.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_specfun.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_sobol.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmvnt_cy.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_qmvnt_cy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\stats\\_stats.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\special\\cython_special.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\index.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\Anaconda3\\envs\\COLORSPACE\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('ffi.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Anaconda3\\envs\\COLORSPACE\\python3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('tk86t.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Anaconda3\\envs\\COLORSPACE\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('透射红绿蓝测试.txt', 'E:\\7-31\\透射红绿蓝测试.txt', 'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:/Anaconda3/envs/COLORSPACE/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\Anaconda3\\envs\\COLORSPACE\\Lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'E:\\7-31\\build\\ColorConverter\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
