#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
打包脚本 - 将main2.py打包为exe文件
"""

import subprocess
import sys
import os
from pathlib import Path

def build_exe():
    """使用PyInstaller打包exe文件"""
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个exe文件
        '--windowed',  # 不显示控制台窗口（如果需要看输出可以去掉这个参数）
        '--add-data', '透射红绿蓝测试.txt;.',  # 包含数据文件
        '--name', 'ColorConverter',  # 指定exe文件名
        '--clean',  # 清理临时文件
        'main2.py'
    ]
    
    print("开始打包...")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("打包成功！")
        print("输出信息:")
        print(result.stdout)
        
        # 检查生成的文件
        exe_path = script_dir / 'dist' / 'ColorConverter.exe'
        if exe_path.exists():
            print(f"\n✅ exe文件已生成: {exe_path}")
            print(f"文件大小: {exe_path.stat().st_size / (1024*1024):.2f} MB")
        else:
            print("❌ 未找到生成的exe文件")
            
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败!")
        print("错误信息:")
        print(e.stderr)
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = build_exe()
    if success:
        print("\n🎉 打包完成！exe文件位于 dist/ 目录中")
    else:
        print("\n💥 打包失败，请检查错误信息")
    
    input("按回车键退出...")
